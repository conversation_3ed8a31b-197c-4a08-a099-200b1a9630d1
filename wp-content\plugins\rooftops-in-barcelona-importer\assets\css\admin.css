/**
 * Admin styles for Rooftops in Barcelona Importer
 */

/* Import Page */
.rib-import-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
}

.rib-import-section {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.rib-import-form {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.rib-import-form:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rib-form-row {
    margin-bottom: 15px;
}

.rib-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

/* Tabs */
.rib-import-tabs, .rib-help-tabs {
    margin-top: 15px;
}

.rib-tab-nav, .rib-help-tabs .rib-tab-nav, .rib-method-tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 20px;
}

.rib-tab-link, .rib-help-tab-link, .rib-method-tab {
    padding: 10px 15px;
    background: #f1f1f1;
    border: 1px solid #ccc;
    border-bottom: none;
    margin-right: 5px;
    cursor: pointer;
    font-weight: 500;
    border-radius: 3px 3px 0 0;
}

.rib-tab-link.active, .rib-help-tab-link.active, .rib-method-tab.active {
    background: #fff;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    font-weight: 600;
}

.rib-tab-content, .rib-help-tab-content, .rib-method-content {
    display: none;
    padding: 15px 0;
}

.rib-tab-content.active, .rib-help-tab-content.active, .rib-method-content.active {
    display: block;
}

/* Import Methods */
.rib-import-methods {
    margin-top: 15px;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    background: #f9f9f9;
}

.rib-method-tabs {
    margin-top: 0;
    margin-bottom: 15px;
}

#direct-upload-form {
    margin-top: 10px;
}

#direct-json-file {
    padding: 10px;
    border: 1px dashed #ccc;
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
}

/* File Upload */
.rib-file-upload-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.rib-selected-file {
    margin-left: 10px;
    padding: 5px 10px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
    flex-grow: 1;
}

.rib-directory-input-container {
    display: flex;
    gap: 10px;
}

.rib-directory-input-container input {
    flex-grow: 1;
}

/* Progress Bar */
.rib-progress-container {
    margin: 15px 0;
    background: #f1f1f1;
    border-radius: 3px;
    position: relative;
    height: 25px;
    overflow: hidden;
}

.rib-progress-bar {
    background: #0073aa;
    height: 100%;
    width: 0;
    transition: width 0.3s ease;
}

.rib-progress-status {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    line-height: 25px;
    color: #fff;
    font-weight: 600;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

/* Debug Mode */
.rib-checkbox-label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.rib-checkbox-label input {
    margin-right: 8px;
}

/* Import Results */
.rib-import-results {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
}

.rib-import-result {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.rib-import-result:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rib-import-success {
    color: #46b450;
}

.rib-import-error {
    color: #dc3232;
}

.rib-import-warning {
    color: #ffb900;
}

.rib-import-info {
    color: #0073aa;
}

.rib-debug-details {
    margin-top: 10px;
    padding: 10px;
    background: #f1f1f1;
    border-left: 3px solid #0073aa;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    display: none;
}

.rib-debug-toggle {
    margin-top: 5px;
    color: #0073aa;
    cursor: pointer;
    font-size: 12px;
    text-decoration: underline;
}

/* Help Section */
.rib-import-help {
    margin-top: 30px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.rib-import-help pre {
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    overflow-x: auto;
}

.rib-troubleshooting-list {
    margin: 0;
    padding: 0;
}

.rib-troubleshooting-list li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.rib-troubleshooting-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.rib-troubleshooting-list strong {
    display: block;
    margin-bottom: 5px;
}

/* Meta Boxes */
.rib-meta-box {
    margin: 0 -12px;
}

.rib-images-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.rib-image-item {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    overflow: hidden;
}

.rib-image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.rib-image-info {
    padding: 10px;
    background-color: #f9f9f9;
    font-size: 12px;
}

.rib-reviews-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.rib-review-item {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    background-color: #f9f9f9;
}

.rib-review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.rib-review-author {
    font-weight: 600;
}

.rib-review-rating {
    color: #ffb900;
}

.rib-review-content {
    margin-bottom: 10px;
    font-style: italic;
}

.rib-review-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #777;
}

/* Dashboard Widget */
.rib-dashboard-widget {
    margin: -12px;
}

.rib-stats {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;
}

.rib-stat-item {
    flex: 1;
    min-width: 80px;
    text-align: center;
    padding: 15px 10px;
    border-right: 1px solid #eee;
}

.rib-stat-item:last-child {
    border-right: none;
}

.rib-stat-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #0073aa;
}

.rib-stat-label {
    display: block;
    font-size: 13px;
    color: #777;
}

.rib-recent-spas {
    padding: 0 15px;
    margin-bottom: 20px;
}

.rib-recent-spas h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.rib-recent-spas ul {
    margin: 0;
}

.rib-recent-spas li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.rib-recent-spas li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.rib-spa-date {
    color: #777;
    font-size: 12px;
}

.rib-actions {
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

/* Tools Page */
.rib-tools-container {
    margin-top: 20px;
}

.rib-tools-section {
    margin-bottom: 30px;
}

.rib-tool-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 15px;
}

.rib-tool-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.rib-tool-card p {
    margin-bottom: 20px;
}

/* Welcome Notice */
.rib-welcome-notice {
    padding: 20px;
}

.rib-welcome-notice h3 {
    margin-top: 0;
}
