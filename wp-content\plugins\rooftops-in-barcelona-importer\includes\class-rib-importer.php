<?php
/**
 * Importer class
 *
 * Handles importing rooftop data from JSON files
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class RIB_Importer {
    /**
     * Constructor
     */
    public function __construct() {
        // Add AJAX handlers
        add_action( 'wp_ajax_rib_import_json', array( $this, 'ajax_import_json' ) );
        add_action( 'wp_ajax_rib_import_all_json', array( $this, 'ajax_import_all_json' ) );
        add_action( 'wp_ajax_rib_import_json_by_id', array( $this, 'ajax_import_json_by_id' ) );
        add_action( 'wp_ajax_rib_upload_and_import_json', array( $this, 'ajax_upload_and_import_json' ) );

        // Register the rooftop post type with the post_tag taxonomy - This was for old 'Tags', now handled by 'popular' taxonomy.
        // add_action('init', array($this, 'register_rooftop_post_type_with_tags'), 20); // Commented out
    }

    /**
     * AJAX handler for uploading and importing a JSON file directly
     */
    public function ajax_upload_and_import_json() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'rib_import_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to import data', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check if file was uploaded
        if ( empty( $_FILES['json_file'] ) ) {
            wp_send_json_error( array( 'message' => __( 'No file was uploaded', 'rooftops-in-barcelona-importer' ) ) );
        }

        $file = $_FILES['json_file'];
        $debug_mode = isset( $_POST['debug_mode'] ) && $_POST['debug_mode'] === 'true';

        // Check for upload errors
        if ( $file['error'] !== UPLOAD_ERR_OK ) {
            $error_message = $this->get_upload_error_message( $file['error'] );
            wp_send_json_error( array(
                'message' => $error_message,
                'debug' => $debug_mode ? "Upload error code: {$file['error']}" : null
            ) );
        }

        // Check file type
        $file_info = pathinfo( $file['name'] );
        if ( strtolower( $file_info['extension'] ) !== 'json' ) {
            wp_send_json_error( array( 'message' => __( 'The uploaded file is not a JSON file', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Use WordPress upload directory instead of system temp directory
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/rooftop-importer-temp';

        // Create temp directory if it doesn't exist
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }

        // Create a unique filename
        $temp_file = $temp_dir . '/' . sanitize_file_name($file['name']);

        // Move the uploaded file to our temp directory
        if (!move_uploaded_file($file['tmp_name'], $temp_file)) {
            wp_send_json_error( array(
                'message' => __( 'Failed to move uploaded file', 'rooftops-in-barcelona-importer' ),
                'debug' => $debug_mode ? "Source: {$file['tmp_name']}, Destination: {$temp_file}" : null
            ) );
        }

        // Verify the file exists and is readable
        if (!file_exists($temp_file) || !is_readable($temp_file)) {
            wp_send_json_error( array(
                'message' => __( 'Cannot access the uploaded file', 'rooftops-in-barcelona-importer' ),
                'debug' => $debug_mode ? "File path: {$temp_file}, Exists: " . (file_exists($temp_file) ? 'Yes' : 'No') . ", Readable: " . (is_readable($temp_file) ? 'Yes' : 'No') : null
            ) );
        }

        // Read the file content directly
        $file_content = file_get_contents($temp_file);
        if ($file_content === false) {
            wp_send_json_error( array(
                'message' => __( 'Failed to read the uploaded file', 'rooftops-in-barcelona-importer' ),
                'debug' => $debug_mode ? "File path: {$temp_file}, Size: " . filesize($temp_file) . " bytes" : null
            ) );
        }

        // Validate JSON before importing
        $json_data = json_decode($file_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Delete the temporary file
            @unlink($temp_file);

            wp_send_json_error( array(
                'message' => sprintf(__( 'Error decoding JSON: %s', 'rooftops-in-barcelona-importer' ), json_last_error_msg()),
                'debug' => $debug_mode ? "JSON Error: " . json_last_error_msg() . "\nFirst 100 chars: " . substr($file_content, 0, 100) : null
            ) );
        }

        // Import the file
        $result = $this->import_json_file($temp_file, $debug_mode);

        // Delete the temporary file
        @unlink($temp_file);

        if (is_wp_error($result)) {
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
                'debug' => $debug_mode ? $result->get_error_data() : null
            ) );
        }

        wp_send_json_success( array(
            'message' => sprintf( __( 'Successfully imported "%s"', 'rooftops-in-barcelona-importer' ), $result['title'] ),
            'post_id' => $result['post_id'],
            'debug' => $debug_mode ? $result['debug'] : null
        ) );
    }

    /**
     * Get upload error message
     *
     * @param int $error_code PHP upload error code
     * @return string Human-readable error message
     */
    private function get_upload_error_message( $error_code ) {
        switch ( $error_code ) {
            case UPLOAD_ERR_INI_SIZE:
                return __( 'The uploaded file exceeds the upload_max_filesize directive in php.ini', 'rooftops-in-barcelona-importer' );
            case UPLOAD_ERR_FORM_SIZE:
                return __( 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form', 'rooftops-in-barcelona-importer' );
            case UPLOAD_ERR_PARTIAL:
                return __( 'The uploaded file was only partially uploaded', 'rooftops-in-barcelona-importer' );
            case UPLOAD_ERR_NO_FILE:
                return __( 'No file was uploaded', 'rooftops-in-barcelona-importer' );
            case UPLOAD_ERR_NO_TMP_DIR:
                return __( 'Missing a temporary folder', 'rooftops-in-barcelona-importer' );
            case UPLOAD_ERR_CANT_WRITE:
                return __( 'Failed to write file to disk', 'rooftops-in-barcelona-importer' );
            case UPLOAD_ERR_EXTENSION:
                return __( 'A PHP extension stopped the file upload', 'rooftops-in-barcelona-importer' );
            default:
                return __( 'Unknown upload error', 'rooftops-in-barcelona-importer' );
        }
    }

    /**
     * AJAX handler for importing a single JSON file
     */
    public function ajax_import_json() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'rib_import_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to import data', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check if file path is provided
        if ( empty( $_POST['file_path'] ) ) {
            wp_send_json_error( array( 'message' => __( 'No file path provided', 'rooftops-in-barcelona-importer' ) ) );
        }

        $file_path = sanitize_text_field( $_POST['file_path'] );
        $debug_mode = isset( $_POST['debug_mode'] ) && $_POST['debug_mode'] === 'true';

        // Import the file
        $result = $this->import_json_file( $file_path, $debug_mode );

        if ( is_wp_error( $result ) ) {
            $debug_info = $debug_mode ? $this->get_debug_info( $file_path ) : null;
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
                'debug' => $debug_info
            ) );
        }

        wp_send_json_success( array(
            'message' => sprintf( __( 'Successfully imported "%s"', 'rooftops-in-barcelona-importer' ), $result['title'] ),
            'post_id' => $result['post_id'],
            'debug' => $debug_mode ? $result['debug'] : null
        ) );
    }

    /**
     * AJAX handler for importing a JSON file by media ID
     */
    public function ajax_import_json_by_id() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'rib_import_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to import data', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check if file ID is provided
        if ( empty( $_POST['file_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'No file ID provided', 'rooftops-in-barcelona-importer' ) ) );
        }

        $file_id = intval( $_POST['file_id'] );
        $debug_mode = isset( $_POST['debug_mode'] ) && $_POST['debug_mode'] === 'true';

        // Get file path from media ID
        $file_path = get_attached_file( $file_id );

        if ( ! $file_path ) {
            wp_send_json_error( array( 'message' => __( 'Could not find the file in the media library', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check if it's a JSON file
        if ( ! preg_match( '/\.json$/i', $file_path ) ) {
            wp_send_json_error( array( 'message' => __( 'The selected file is not a JSON file', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Import the file
        $result = $this->import_json_file( $file_path, $debug_mode );

        if ( is_wp_error( $result ) ) {
            $debug_info = $debug_mode ? $this->get_debug_info( $file_path ) : null;
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
                'debug' => $debug_info
            ) );
        }

        wp_send_json_success( array(
            'message' => sprintf( __( 'Successfully imported "%s"', 'rooftops-in-barcelona-importer' ), $result['title'] ),
            'post_id' => $result['post_id'],
            'debug' => $debug_mode ? $result['debug'] : null
        ) );
    }

    /**
     * AJAX handler for importing all JSON files in a directory
     */
    public function ajax_import_all_json() {
        // Check nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'rib_import_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'You do not have permission to import data', 'rooftops-in-barcelona-importer' ) ) );
        }

        // Check if directory path is provided
        if ( empty( $_POST['directory'] ) ) {
            wp_send_json_error( array( 'message' => __( 'No directory path provided', 'rooftops-in-barcelona-importer' ) ) );
        }

        $directory = sanitize_text_field( $_POST['directory'] );
        $debug_mode = isset( $_POST['debug_mode'] ) && $_POST['debug_mode'] === 'true';

        // Get all JSON files in the directory
        $files = glob( $directory . '/*.json' );

        if ( empty( $files ) ) {
            wp_send_json_error( array( 'message' => __( 'No JSON files found in the specified directory', 'rooftops-in-barcelona-importer' ) ) );
        }

        $results = array(
            'success' => 0,
            'failed' => 0,
            'total' => count( $files ),
            'imported' => array(),
            'failed_files' => array(),
        );

        // Import each file
        foreach ( $files as $file ) {
            $result = $this->import_json_file( $file, $debug_mode );

            if ( is_wp_error( $result ) ) {
                $results['failed']++;
                $results['failed_files'][] = array(
                    'file' => basename( $file ),
                    'error' => $result->get_error_message(),
                    'debug' => $debug_mode ? $this->get_debug_info( $file ) : null
                );
            } else {
                $results['success']++;
                $results['imported'][] = array(
                    'title' => $result['title'],
                    'post_id' => $result['post_id'],
                    'debug' => $debug_mode ? $result['debug'] : null
                );
            }
        }

        wp_send_json_success( array(
            'message' => sprintf(
                __( 'Import completed. Successfully imported %1$d out of %2$d files.', 'rooftops-in-barcelona-importer' ),
                $results['success'],
                $results['total']
            ),
            'results' => $results,
            'debug' => $debug_mode ? 'Processed ' . count($files) . ' files from directory: ' . $directory : null
        ) );
    }

    /**
     * Import a single JSON file
     *
     * @param string $file_path Path to the JSON file
     * @param bool $debug_mode Whether to include debug information
     * @return array|WP_Error Import result or error
     */
    public function import_json_file( $file_path, $debug_mode = false ) {
        $debug_info = '';

        // Check if file exists
        if ( ! file_exists( $file_path ) ) {
            // Try to find the file in the rooftopdata directory if it's a relative path
            $alt_path = ABSPATH . 'rooftopdata/rooftops/' . basename($file_path);
            if (file_exists($alt_path)) {
                $file_path = $alt_path;
                if ($debug_mode) {
                    $debug_info .= "Original file not found, using alternative path: " . $file_path . "\n";
                }
            } else {
                return new WP_Error( 'file_not_found', sprintf( __( 'File not found: %s', 'rooftops-in-barcelona-importer' ), $file_path ) );
            }
        }

        if ( $debug_mode ) {
            $debug_info .= "File path: " . $file_path . "\n";
            $debug_info .= "File size: " . filesize( $file_path ) . " bytes\n";
        }

        // Read and decode JSON file
        $json_content = file_get_contents( $file_path );

        if ( $debug_mode ) {
            $debug_info .= "JSON content length: " . strlen( $json_content ) . " characters\n";
        }

        $rooftop_data = json_decode( $json_content, true );
        $json_error = json_last_error();

        if ( $json_error !== JSON_ERROR_NONE ) {
            if ( $debug_mode ) {
                $debug_info .= "JSON decode error: " . json_last_error_msg() . "\n";
                $debug_info .= "JSON error code: " . $json_error . "\n";
                $debug_info .= "First 100 characters of file: " . substr( $json_content, 0, 100 ) . "...\n";
            }
            return new WP_Error( 'json_error', sprintf( __( 'Error decoding JSON: %s', 'rooftops-in-barcelona-importer' ), json_last_error_msg() ) );
        }

        if ( $debug_mode ) {
            $debug_info .= "JSON decoded successfully\n";
            $debug_info .= "Top-level keys: " . implode(', ', array_keys($rooftop_data)) . "\n";
        }

        // Check if required data exists
        if ( empty( $rooftop_data['name'] ) ) {
            if ( $debug_mode ) {
                $debug_info .= "Error: Missing required 'name' field\n";
            }
            return new WP_Error( 'missing_data', __( 'Missing required data: name', 'rooftops-in-barcelona-importer' ) );
        }

        if ( $debug_mode ) {
            $debug_info .= "Rooftop name: " . $rooftop_data['name'] . "\n";
        }

        // Check if rooftop already exists
        $existing_rooftop = get_page_by_title( $rooftop_data['name'], OBJECT, 'rooftop' );

        if ( $debug_mode ) {
            $debug_info .= "Rooftop exists: " . ($existing_rooftop ? "Yes (ID: {$existing_rooftop->ID})" : "No") . "\n";
        }

        // Prepare post data
        $post_data = array(
            'post_title'   => $rooftop_data['name'],
            'post_content' => ! empty( $rooftop_data['description'] ) ? $rooftop_data['description'] : '',
            'post_status'  => 'publish',
            'post_type'    => 'rooftop',
        );

        // Update existing or create new
        if ( $existing_rooftop ) {
            $post_data['ID'] = $existing_rooftop->ID;
            $post_id = wp_update_post( $post_data );

            if ( $debug_mode ) {
                $debug_info .= "Updated existing rooftop post (ID: {$post_id})\n";
            }
        } else {
            $post_id = wp_insert_post( $post_data );

            if ( $debug_mode ) {
                $debug_info .= "Created new rooftop post (ID: {$post_id})\n";
            }
        }

        if ( is_wp_error( $post_id ) ) {
            if ( $debug_mode ) {
                $debug_info .= "Error creating/updating post: " . $post_id->get_error_message() . "\n";
            }
            return $post_id;
        }

        // Save meta data
        $meta_result = $this->save_rooftop_meta_data( $post_id, $rooftop_data );

        if ( $debug_mode ) {
            $debug_info .= "Meta data saved: " . count($meta_result) . " fields\n";
            $debug_info .= "Meta fields: " . implode(', ', $meta_result) . "\n";
        }

        // Process taxonomies
        $taxonomy_result = $this->process_rooftop_taxonomies( $post_id, $rooftop_data );

        if ( $debug_mode ) {
            $debug_info .= "Taxonomies processed: " . count($taxonomy_result) . " taxonomies\n";
            foreach ( $taxonomy_result as $taxonomy => $terms ) {
                $debug_info .= "- {$taxonomy}: " . count($terms) . " terms\n";
            }
        }

        return array(
            'post_id' => $post_id,
            'title'   => $rooftop_data['name'],
            'debug'   => $debug_mode ? $debug_info : null
        );
    }

    /**
     * Get debug information for a JSON file
     *
     * @param string $file_path Path to the JSON file
     * @return string Debug information
     */
    private function get_debug_info( $file_path ) {
        $debug_info = "Debug Information:\n";
        $debug_info .= "File: " . basename( $file_path ) . "\n";
        $debug_info .= "Full path: " . $file_path . "\n";

        if ( ! file_exists( $file_path ) ) {
            $debug_info .= "Error: File does not exist\n";
            return $debug_info;
        }

        $debug_info .= "File size: " . filesize( $file_path ) . " bytes\n";
        $debug_info .= "Last modified: " . date( 'Y-m-d H:i:s', filemtime( $file_path ) ) . "\n";

        // Try to read the file
        $file_content = @file_get_contents( $file_path );

        if ( $file_content === false ) {
            $debug_info .= "Error: Could not read file content\n";
            return $debug_info;
        }

        $debug_info .= "Content length: " . strlen( $file_content ) . " characters\n";

        // Check if it's valid JSON
        $json_data = json_decode( $file_content, true );
        $json_error = json_last_error();

        if ( $json_error !== JSON_ERROR_NONE ) {
            $debug_info .= "JSON Error: " . json_last_error_msg() . "\n";
            $debug_info .= "Error code: " . $json_error . "\n";

            // Try to identify the location of the error
            $error_line = $this->find_json_error_line( $file_content );

            if ( $error_line !== false ) {
                $debug_info .= "Error likely near line: " . $error_line['line'] . "\n";
                $debug_info .= "Context: " . $error_line['context'] . "\n";
            }

            // Show the first 100 characters
            $debug_info .= "First 100 characters: " . substr( $file_content, 0, 100 ) . "...\n";
        } else {
            $debug_info .= "JSON is valid\n";
            $debug_info .= "Top-level keys: " . implode(', ', array_keys($json_data)) . "\n";

            // Check for required fields
            $required_fields = array('name');
            foreach ( $required_fields as $field ) {
                $debug_info .= "Required field '{$field}': " . (isset($json_data[$field]) ? "Present" : "Missing") . "\n";
            }
        }

        return $debug_info;
    }

    /**
     * Try to find the line where a JSON error occurs
     *
     * @param string $json_string JSON string to analyze
     * @return array|false Line number and context, or false if not found
     */
    private function find_json_error_line( $json_string ) {
        $lines = explode("\n", $json_string);
        $json_partial = "";

        foreach ( $lines as $line_number => $line ) {
            $json_partial .= $line . "\n";

            // Try to decode the partial JSON
            json_decode($json_partial);

            if ( json_last_error() !== JSON_ERROR_NONE ) {
                // Found the line with the error
                $context = $line;

                // Get a few lines before and after for context
                $start = max(0, $line_number - 2);
                $end = min(count($lines) - 1, $line_number + 2);

                $context = "";
                for ( $i = $start; $i <= $end; $i++ ) {
                    $prefix = ($i === $line_number) ? ">> " : "   ";
                    $context .= $prefix . ($i + 1) . ": " . $lines[$i] . "\n";
                }

                return array(
                    'line' => $line_number + 1,
                    'context' => $context
                );
            }
        }

        return false;
    }

    /**
     * Register the spa post type with the post_tag taxonomy
     */
    // public function register_spa_post_type_with_tags() {
    //     // Make sure the spa post type exists
    //     if (post_type_exists('spa')) {
    //         // Register the post_tag taxonomy for the spa post type
    //         // register_taxonomy_for_object_type('post_tag', 'spa'); // Commented out as 'popular' taxonomy is now used
    //     }
    // }

    /**
     * Re-import popular features for all spa posts to the 'popular' taxonomy.
     */
    public function reimport_tags_for_all_spas() {
        // Get all spa posts
        $spa_posts = get_posts(array(
            'post_type' => 'spa',
            'posts_per_page' => -1,
            'post_status' => 'publish',
        ));

        $updated_count = 0;

        foreach ($spa_posts as $spa_post) {
            // Get popular features from post meta (JSON 'popular' key)
            $popular_terms_from_meta = get_post_meta($spa_post->ID, 'popular', true);

            // If popular is empty, try to get from 'tags' meta key (backward compatibility from JSON)
            if (empty($popular_terms_from_meta)) {
                $popular_terms_from_meta = get_post_meta($spa_post->ID, 'tags', true);
            }

            if (!empty($popular_terms_from_meta) && is_array($popular_terms_from_meta)) {
                // Ensure terms exist in the 'popular' taxonomy and set them
                $term_ids_to_set = array();
                foreach($popular_terms_from_meta as $term_name) {
                    $term = term_exists( $term_name, 'popular' );
                    if ( ! $term ) {
                        $term = wp_insert_term( $term_name, 'popular' );
                    }
                    if ( ! is_wp_error( $term ) ) {
                        $term_ids_to_set[] = (int) $term['term_id'];
                    }
                }
                if (!empty($term_ids_to_set)) {
                    wp_set_object_terms($spa_post->ID, $term_ids_to_set, 'popular', false); // false to replace existing popular terms
                    $updated_count++;
                }
            }
        }
        return $updated_count;
    }

    /**
     * Save rooftop meta data
     *
     * @param int $post_id Post ID
     * @param array $rooftop_data Rooftop data from JSON
     * @return array List of saved meta fields
     */
    private function save_rooftop_meta_data( $post_id, $rooftop_data ) {
        $saved_fields = array();

        // Save hostVenue data
        if ( ! empty( $rooftop_data['hostVenue'] ) ) {
            update_post_meta( $post_id, 'host_venue', $rooftop_data['hostVenue'] );
            $saved_fields[] = 'host_venue';
        }

        // Save basicInfo data
        if ( ! empty( $rooftop_data['basicInfo'] ) ) {
            update_post_meta( $post_id, 'basic_info', $rooftop_data['basicInfo'] );
            $saved_fields[] = 'basic_info';
        }

        // Save experienceAndVibe data
        if ( ! empty( $rooftop_data['experienceAndVibe'] ) ) {
            update_post_meta( $post_id, 'experience_and_vibe', $rooftop_data['experienceAndVibe'] );
            $saved_fields[] = 'experience_and_vibe';
        }

        // Save accessibility data
        if ( ! empty( $rooftop_data['accessibility'] ) ) {
            update_post_meta( $post_id, 'rooftop_accessibility', $rooftop_data['accessibility'] );
            $saved_fields[] = 'rooftop_accessibility';
        }

        // Save foodAndDrinks data
        if ( ! empty( $rooftop_data['foodAndDrinks'] ) ) {
            update_post_meta( $post_id, 'food_and_drinks', $rooftop_data['foodAndDrinks'] );
            $saved_fields[] = 'food_and_drinks';
        }

        // Save location data (backward compatibility)
        if ( ! empty( $rooftop_data['location'] ) ) {
            update_post_meta( $post_id, 'location', $rooftop_data['location'] );
            $saved_fields[] = 'location';
        }

        // Save contact data (backward compatibility)
        if ( ! empty( $rooftop_data['contact'] ) ) {
            update_post_meta( $post_id, 'contact', $rooftop_data['contact'] );
            $saved_fields[] = 'contact';
        }

        // Save social media data
        if ( ! empty( $rooftop_data['social_media'] ) ) {
            update_post_meta( $post_id, 'social_media', $rooftop_data['social_media'] );
            $saved_fields[] = 'social_media';
        }

        // Save services (backward compatibility)
        if ( ! empty( $rooftop_data['services'] ) ) {
            update_post_meta( $post_id, 'services', $rooftop_data['services'] );
            $saved_fields[] = 'services';
        }

        // Save amenities (backward compatibility)
        if ( ! empty( $rooftop_data['amenities'] ) ) {
            update_post_meta( $post_id, 'amenities', $rooftop_data['amenities'] );
            $saved_fields[] = 'amenities';
        }

        // Save practicalitiesForTourists data
        if ( ! empty( $rooftop_data['practicalitiesForTourists'] ) ) {
            update_post_meta( $post_id, 'practicalities_for_tourists', $rooftop_data['practicalitiesForTourists'] );
            $saved_fields[] = 'practicalities_for_tourists';
        }

        // Save hours - support both old and new format
        if ( ! empty( $rooftop_data['hours'] ) ) {
            update_post_meta( $post_id, 'hours', $rooftop_data['hours'] );
            $saved_fields[] = 'hours';
        }

        // Save hoursDisplay if available (new format)
        if ( ! empty( $rooftop_data['hoursDisplay'] ) ) {
            update_post_meta( $post_id, 'hoursDisplay', $rooftop_data['hoursDisplay'] );
            $saved_fields[] = 'hoursDisplay';
        }

        // Save hoursSchema if available (new format)
        if ( ! empty( $rooftop_data['hoursSchema'] ) ) {
            update_post_meta( $post_id, 'hoursSchema', $rooftop_data['hoursSchema'] );
            $saved_fields[] = 'hoursSchema';
        }

        // Save specialties (backward compatibility)
        if ( ! empty( $rooftop_data['specialties'] ) ) {
            update_post_meta( $post_id, 'specialties', $rooftop_data['specialties'] );
            $saved_fields[] = 'specialties';
        }

        // Save staff (backward compatibility)
        if ( ! empty( $rooftop_data['staff'] ) ) {
            update_post_meta( $post_id, 'staff', $rooftop_data['staff'] );
            $saved_fields[] = 'staff';
        }

        // Save awards (backward compatibility)
        if ( ! empty( $rooftop_data['awards'] ) ) {
            update_post_meta( $post_id, 'awards', $rooftop_data['awards'] );
            $saved_fields[] = 'awards';
        }

        // Save packages (backward compatibility)
        if ( ! empty( $rooftop_data['packages'] ) ) {
            update_post_meta( $post_id, 'packages', $rooftop_data['packages'] );
            $saved_fields[] = 'packages';
        }

        // Save sustainability (backward compatibility)
        if ( ! empty( $rooftop_data['sustainability'] ) ) {
            update_post_meta( $post_id, 'sustainability', $rooftop_data['sustainability'] );
            $saved_fields[] = 'sustainability';
        }

        // Save transportation
        if ( ! empty( $rooftop_data['transportation'] ) ) {
            update_post_meta( $post_id, 'transportation', $rooftop_data['transportation'] );
            $saved_fields[] = 'transportation';
        }

        // Save nearby attractions
        if ( ! empty( $rooftop_data['nearby_attractions'] ) ) {
            update_post_meta( $post_id, 'nearby_attractions', $rooftop_data['nearby_attractions'] );
            $saved_fields[] = 'nearby_attractions';
        }

        // Save history
        if ( ! empty( $rooftop_data['history'] ) ) {
            update_post_meta( $post_id, 'history', $rooftop_data['history'] );
            $saved_fields[] = 'history';
        }

        // Save images
        if ( ! empty( $rooftop_data['images'] ) ) {
            update_post_meta( $post_id, 'images', $rooftop_data['images'] );
            $saved_fields[] = 'images';

            // Set the first image as featured image if available
            if ( ! empty( $rooftop_data['images'][0]['url'] ) ) {
                $featured_image_result = $this->set_featured_image_from_url( $post_id, $rooftop_data['images'][0]['url'], $rooftop_data['name'] );
                if ( $featured_image_result ) {
                    $saved_fields[] = 'featured_image';
                }
            }
        }

        // Save reviews
        if ( ! empty( $rooftop_data['reviews'] ) ) {
            update_post_meta( $post_id, 'reviews', $rooftop_data['reviews'] );
            $saved_fields[] = 'reviews';
        }

        // Save popular features
        if ( ! empty( $rooftop_data['popular'] ) ) {
            update_post_meta( $post_id, 'popular', $rooftop_data['popular'] );
            $saved_fields[] = 'popular';
        }
        // For backward compatibility with old JSON files
        elseif ( ! empty( $rooftop_data['tags'] ) ) {
            update_post_meta( $post_id, 'popular', $rooftop_data['tags'] );
            $saved_fields[] = 'popular (from tags)';
        }

        // Save FAQ data
        if ( ! empty( $rooftop_data['faq'] ) ) {
            update_post_meta( $post_id, 'faq', $rooftop_data['faq'] );
            $saved_fields[] = 'faq';
        }

        // Save meta description
        if ( ! empty( $rooftop_data['meta_description'] ) ) {
            update_post_meta( $post_id, 'meta_description', sanitize_text_field( $rooftop_data['meta_description'] ) );
            $saved_fields[] = 'meta_description';
        }

        // Save pros and cons
        if ( ! empty( $rooftop_data['pros_cons'] ) ) {
            update_post_meta( $post_id, 'pros_cons', $rooftop_data['pros_cons'] );
            $saved_fields[] = 'pros_cons';
        }

        // Calculate and save average price
        if ( ! empty( $rooftop_data['packages'] ) ) {
            $this->calculate_average_price( $post_id, $rooftop_data['packages'] );
            $saved_fields[] = 'average_price';
        }

        return $saved_fields;
    }

    /**
     * Process rooftop taxonomies
     *
     * @param int $post_id Post ID
     * @param array $rooftop_data Rooftop data from JSON
     * @return array Processed taxonomies and their terms
     */
    private function process_rooftop_taxonomies( $post_id, $rooftop_data ) {
        $processed_taxonomies = array();

        // Process Neighborhood (from JSON 'location.neighborhood' or 'basicInfo.neighborhood')
        $neighborhood = '';
        if ( ! empty( $rooftop_data['basicInfo']['neighborhood'] ) ) {
            $neighborhood = $rooftop_data['basicInfo']['neighborhood'];
        } elseif ( ! empty( $rooftop_data['location']['neighborhood'] ) ) {
            $neighborhood = $rooftop_data['location']['neighborhood'];
        }

        if ( ! empty( $neighborhood ) ) {
            $term = term_exists( $neighborhood, 'neighborhood' );
            if ( ! $term ) {
                $term = wp_insert_term( $neighborhood, 'neighborhood', array(
                    'description' => sprintf( __( 'Rooftops located in the %s neighborhood of Barcelona', 'rooftops-in-barcelona-importer' ), $neighborhood ),
                ) );
            }
            if ( ! is_wp_error( $term ) ) {
                wp_set_object_terms( $post_id, (int) $term['term_id'], 'neighborhood', false );
                $processed_taxonomies['neighborhood'] = array( $neighborhood );
            }
        }

        // Process Atmosphere (from JSON 'experienceAndVibe.atmosphere')
        if ( ! empty( $rooftop_data['experienceAndVibe']['atmosphere'] ) ) {
            $atmosphere_terms = is_array( $rooftop_data['experienceAndVibe']['atmosphere'] )
                ? $rooftop_data['experienceAndVibe']['atmosphere']
                : array( $rooftop_data['experienceAndVibe']['atmosphere'] );

            $atmosphere_term_ids = array();
            $atmosphere_term_names = array();

            foreach ( $atmosphere_terms as $atmosphere_name ) {
                $term = term_exists( $atmosphere_name, 'atmosphere' );
                if ( ! $term ) {
                    $term = wp_insert_term( $atmosphere_name, 'atmosphere' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $atmosphere_term_ids[] = (int) $term['term_id'];
                    $atmosphere_term_names[] = $atmosphere_name;
                }
            }
            if ( ! empty( $atmosphere_term_ids ) ) {
                wp_set_object_terms( $post_id, $atmosphere_term_ids, 'atmosphere', false );
                $processed_taxonomies['atmosphere'] = $atmosphere_term_names;
            }
        }

        // Process Best For (from JSON 'experienceAndVibe.bestFor')
        if ( ! empty( $rooftop_data['experienceAndVibe']['bestFor'] ) ) {
            $best_for_terms = is_array( $rooftop_data['experienceAndVibe']['bestFor'] )
                ? $rooftop_data['experienceAndVibe']['bestFor']
                : array( $rooftop_data['experienceAndVibe']['bestFor'] );

            $best_for_term_ids = array();
            $best_for_term_names = array();

            foreach ( $best_for_terms as $best_for_name ) {
                $term = term_exists( $best_for_name, 'best_for' );
                if ( ! $term ) {
                    $term = wp_insert_term( $best_for_name, 'best_for' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $best_for_term_ids[] = (int) $term['term_id'];
                    $best_for_term_names[] = $best_for_name;
                }
            }
            if ( ! empty( $best_for_term_ids ) ) {
                wp_set_object_terms( $post_id, $best_for_term_ids, 'best_for', false );
                $processed_taxonomies['best_for'] = $best_for_term_names;
            }
        }

        // Process View Type (from JSON 'experienceAndVibe.viewType')
        if ( ! empty( $rooftop_data['experienceAndVibe']['viewType'] ) ) {
            $view_type_terms = is_array( $rooftop_data['experienceAndVibe']['viewType'] )
                ? $rooftop_data['experienceAndVibe']['viewType']
                : array( $rooftop_data['experienceAndVibe']['viewType'] );

            $view_type_term_ids = array();
            $view_type_term_names = array();

            foreach ( $view_type_terms as $view_type_name ) {
                $term = term_exists( $view_type_name, 'view_type' );
                if ( ! $term ) {
                    $term = wp_insert_term( $view_type_name, 'view_type' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $view_type_term_ids[] = (int) $term['term_id'];
                    $view_type_term_names[] = $view_type_name;
                }
            }
            if ( ! empty( $view_type_term_ids ) ) {
                wp_set_object_terms( $post_id, $view_type_term_ids, 'view_type', false );
                $processed_taxonomies['view_type'] = $view_type_term_names;
            }
        }

        // Process Amenities (from JSON 'amenities' key)
        if ( ! empty( $rooftop_data['amenities'] ) ) {
            $amenity_term_ids = array();
            $amenity_term_names = array();
            foreach ( $rooftop_data['amenities'] as $amenity_name ) {
                $term = term_exists( $amenity_name, 'amenities' );
                if ( ! $term ) {
                    $term = wp_insert_term( $amenity_name, 'amenities' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $amenity_term_ids[] = (int) $term['term_id'];
                    $amenity_term_names[] = $amenity_name;
                }
            }
            if ( ! empty( $amenity_term_ids ) ) {
                wp_set_object_terms( $post_id, $amenity_term_ids, 'amenities', false );
                $processed_taxonomies['amenities'] = $amenity_term_names;
            }
        }

        // Process Menu Type (from JSON 'foodAndDrinks.menuType')
        if ( ! empty( $rooftop_data['foodAndDrinks']['menuType'] ) ) {
            $menu_type = $rooftop_data['foodAndDrinks']['menuType'];
            $term = term_exists( $menu_type, 'menu_type' );
            if ( ! $term ) {
                $term = wp_insert_term( $menu_type, 'menu_type' );
            }
            if ( ! is_wp_error( $term ) ) {
                wp_set_object_terms( $post_id, (int) $term['term_id'], 'menu_type', false );
                $processed_taxonomies['menu_type'] = array( $menu_type );
            }
        }

        // Process Cuisine Style (from JSON 'foodAndDrinks.cuisineStyle')
        if ( ! empty( $rooftop_data['foodAndDrinks']['cuisineStyle'] ) ) {
            $cuisine_style_terms = is_array( $rooftop_data['foodAndDrinks']['cuisineStyle'] )
                ? $rooftop_data['foodAndDrinks']['cuisineStyle']
                : array( $rooftop_data['foodAndDrinks']['cuisineStyle'] );

            $cuisine_style_term_ids = array();
            $cuisine_style_term_names = array();

            foreach ( $cuisine_style_terms as $cuisine_style_name ) {
                $term = term_exists( $cuisine_style_name, 'cuisine_style' );
                if ( ! $term ) {
                    $term = wp_insert_term( $cuisine_style_name, 'cuisine_style' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $cuisine_style_term_ids[] = (int) $term['term_id'];
                    $cuisine_style_term_names[] = $cuisine_style_name;
                }
            }
            if ( ! empty( $cuisine_style_term_ids ) ) {
                wp_set_object_terms( $post_id, $cuisine_style_term_ids, 'cuisine_style', false );
                $processed_taxonomies['cuisine_style'] = $cuisine_style_term_names;
            }
        }

        // Process Popular (from JSON 'popular' or 'tags' key - backward compatibility)
        $popular_raw_terms = array();
        if ( ! empty( $rooftop_data['popular'] ) && is_array($rooftop_data['popular'])) {
            $popular_raw_terms = $rooftop_data['popular'];
        } elseif ( ! empty( $rooftop_data['tags'] ) && is_array($rooftop_data['tags'])) { // Backward compatibility
            $popular_raw_terms = $rooftop_data['tags'];
        }

        if ( ! empty( $popular_raw_terms ) ) {
            $popular_term_ids = array();
            $popular_term_names = array();
            foreach ( $popular_raw_terms as $popular_name ) {
                if (empty($popular_name)) continue;
                $term = term_exists( $popular_name, 'popular' );
                if ( ! $term ) {
                    $term = wp_insert_term( $popular_name, 'popular' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $popular_term_ids[] = (int) $term['term_id'];
                    $popular_term_names[] = $popular_name;
                }
            }
            if ( ! empty( $popular_term_ids ) ) {
                wp_set_object_terms( $post_id, $popular_term_ids, 'popular', false );
                $processed_taxonomies['popular'] = $popular_term_names;
            }
        }

        // Process Dietary Options (from JSON 'foodAndDrinks' boolean fields)
        $dietary_options = array();
        if ( ! empty( $rooftop_data['foodAndDrinks']['vegetarian'] ) && $rooftop_data['foodAndDrinks']['vegetarian'] === true ) {
            $dietary_options[] = 'Vegetarian';
        }
        if ( ! empty( $rooftop_data['foodAndDrinks']['vegan'] ) && $rooftop_data['foodAndDrinks']['vegan'] === true ) {
            $dietary_options[] = 'Vegan';
        }
        if ( ! empty( $rooftop_data['foodAndDrinks']['gluten'] ) && $rooftop_data['foodAndDrinks']['gluten'] === true ) {
            $dietary_options[] = 'Gluten-Free';
        }

        if ( ! empty( $dietary_options ) ) {
            $dietary_term_ids = array();
            foreach ( $dietary_options as $dietary_option ) {
                $term = term_exists( $dietary_option, 'dietary_options' );
                if ( ! $term ) {
                    $term = wp_insert_term( $dietary_option, 'dietary_options' );
                }
                if ( ! is_wp_error( $term ) ) {
                    $dietary_term_ids[] = (int) $term['term_id'];
                }
            }
            if ( ! empty( $dietary_term_ids ) ) {
                wp_set_object_terms( $post_id, $dietary_term_ids, 'dietary_options', false );
                $processed_taxonomies['dietary_options'] = $dietary_options;
            }
        }

        return $processed_taxonomies;
    }

    /**
     * Set featured image from URL
     *
     * @param int $post_id Post ID
     * @param string $image_url Image URL
     * @param string $title Optional image title
     * @return bool True on success, false on failure
     */
    private function set_featured_image_from_url( $post_id, $image_url, $title = '' ) {
        // Check if the post already has a featured image
        if ( has_post_thumbnail( $post_id ) ) {
            return true; // Already has featured image
        }

        // Get the file name from the URL
        $file_name = basename( $image_url );

        // Check if the image already exists in the media library
        $attachment_id = $this->get_attachment_id_from_url( $image_url );

        if ( $attachment_id ) {
            // If image exists, set it as the featured image
            $result = set_post_thumbnail( $post_id, $attachment_id );
            return $result ? true : false;
        }

        // Try to download the image
        $image_data = @file_get_contents( $image_url );

        if ( $image_data === false ) {
            return false; // Failed to download image
        }

        // Upload the image
        $upload = wp_upload_bits( $file_name, null, $image_data );

        if ( $upload['error'] ) {
            return false; // Failed to upload image
        }

        // Get file type
        $wp_filetype = wp_check_filetype( $file_name, null );

        if ( empty( $wp_filetype['type'] ) ) {
            return false; // Unknown file type
        }

        // Prepare attachment data
        $attachment = array(
            'post_mime_type' => $wp_filetype['type'],
            'post_title'     => ! empty( $title ) ? $title : preg_replace( '/\.[^.]+$/', '', $file_name ),
            'post_content'   => '',
            'post_status'    => 'inherit',
        );

        // Insert attachment
        $attachment_id = wp_insert_attachment( $attachment, $upload['file'], $post_id );

        if ( is_wp_error( $attachment_id ) ) {
            return false; // Failed to create attachment
        }

        // Include image.php
        require_once( ABSPATH . 'wp-admin/includes/image.php' );

        // Generate attachment metadata
        $attachment_data = wp_generate_attachment_metadata( $attachment_id, $upload['file'] );
        wp_update_attachment_metadata( $attachment_id, $attachment_data );

        // Set as featured image
        $result = set_post_thumbnail( $post_id, $attachment_id );

        return $result ? true : false;
    }

    /**
     * Get attachment ID from URL
     */
    private function get_attachment_id_from_url( $url ) {
        global $wpdb;

        // First, try to get the attachment ID from the URL
        $attachment = $wpdb->get_col( $wpdb->prepare( "SELECT ID FROM $wpdb->posts WHERE guid='%s';", $url ) );

        if ( ! empty( $attachment ) ) {
            return $attachment[0];
        }

        // If not found, try to get it from the file name
        $file_name = basename( $url );
        $attachment = $wpdb->get_col( $wpdb->prepare( "SELECT post_id FROM $wpdb->postmeta WHERE meta_key='_wp_attached_file' AND meta_value LIKE '%%%s';", $file_name ) );

        if ( ! empty( $attachment ) ) {
            return $attachment[0];
        }

        return 0;
    }

    /**
     * Calculate average price from packages
     */
    private function calculate_average_price( $post_id, $packages ) {
        $prices = array();

        foreach ( $packages as $package ) {
            if ( ! empty( $package['price'] ) ) {
                // Check if price is already a number (new format)
                if ( is_numeric( $package['price'] ) ) {
                    $prices[] = (float) $package['price'];
                } else {
                    // Extract numeric value from price string (old format)
                    $price_str = $package['price'];
                    preg_match( '/(\d+)/', $price_str, $matches );

                    if ( ! empty( $matches[1] ) ) {
                        $prices[] = (int) $matches[1];
                    }
                }
            }
        }

        if ( ! empty( $prices ) ) {
            $average_price = array_sum( $prices ) / count( $prices );
            update_post_meta( $post_id, 'average_price', round( $average_price ) );
        }
    }
}
